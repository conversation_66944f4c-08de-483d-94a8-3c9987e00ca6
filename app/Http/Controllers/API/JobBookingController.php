<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\CreateJobBookingRequest;
use App\Http\Requests\API\UpdateJobBookingRequest;
use App\Http\Requests\API\AcceptBidRequest;
use App\Http\Resources\JobBookingResource;
use App\Repositories\API\JobBookingRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\JobBooking;
use App\Models\Business;
use App\Models\Bid;
use Illuminate\Support\Facades\DB;

class JobBookingController extends Controller
{
    protected $repository;

    public function __construct(JobBookingRepository $repository)
    {
        $this->repository = $repository;

        // Apply authorization middleware, except for index method (public access)
        $this->authorizeResource(JobBooking::class, 'jobBooking', [
            'except' => ['index','store']
        ]);
    }

    /**
     * Display a listing of job bookings.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $filters = $request->only(['status', 'job_type', 'service_category']);
            if ($request->filled('user_id')) {
                $filters['user_id'] = $request->input('user_id');
            }
            if ($request->filled('category')) {
                $filters['service_category'] = $request->input('category');
            }
            $perPage = $request->input('per_page', 15);
            
            $jobs = $this->repository->getAll($filters, $perPage);
            
            return response()->json([
                'success' => true,
                'data' => JobBookingResource::collection($jobs),
                'pagination' => [
                    'current_page' => $jobs->currentPage(),
                    'per_page' => $jobs->perPage(),
                    'total' => $jobs->total(),
                    'last_page' => $jobs->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to list jobs: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to list jobs',
                    'details' => $e->getMessage()
                ]
                ], 500);
            }
    }

    /**
     * Store a newly created job booking in storage.
     *
     * @param  \App\Http\Requests\CreateJobBookingRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreateJobBookingRequest $request)
    {
        try {
            $jobData = $request->input('jobData');
            
            // Pass null for userId since we're not requiring authentication for testing
            $job = $this->repository->createJob($jobData, null);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'jobId' => $job->job_uuid,
                    'createdAt' => $job->created_at,
                    'status' => $job->status,
                    'message' => 'Job created successfully. You will be notified when providers respond.'
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create job: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to create job',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Display the specified job booking.
     *
     * @param  \App\Models\JobBooking  $jobBooking
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(JobBooking $jobBooking)
    {
        try {
            return response()->json([
                'success' => true,
                'data' => new JobBookingResource($jobBooking)
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch job: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to fetch job',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Update the specified job booking.
     *
     * @param  \App\Http\Requests\UpdateJobBookingRequest  $request
     * @param  \App\Models\JobBooking  $jobBooking
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateJobBookingRequest $request, JobBooking $jobBooking)
    {
        try {
            $jobData = $request->input('jobData');
            $updatedJob = $this->repository->update($jobBooking, $jobData);

            return response()->json([
                'success' => true,
                'data' => new JobBookingResource($updatedJob),
                'message' => 'Job updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update job: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to update job',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Remove the specified job booking.
     *
     * @param  \App\Models\JobBooking  $jobBooking
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(JobBooking $jobBooking)
    {
        try {
            $this->repository->delete($jobBooking);

            return response()->json([
                'success' => true,
                'message' => 'Job deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete job: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to delete job',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Get matching providers for a job
     *
     * @param string $jobUuid
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProviders($jobUuid)
    {
        try {
            $job = $this->repository->getByUuid($jobUuid);
            
            if (!$job) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_NOT_FOUND',
                        'message' => 'Job not found',
                    ]
                ], 404);
            }
            
            // Build base query for matching providers
            $query = Business::query();
            
            // Match by service category if available
            if ($job->service_category) {
                $query->where('category', $job->service_category);
            }
            
            // Match by location using proper address parsing
            if ($job->state) {
                $query->where(function($q) use ($job) {
                    $q->where('location', 'LIKE', '%' . $job->state . '%')
                      ->orWhere('address', 'LIKE', '%' . $job->state . '%');
                });
            }

            // Get matching businesses
            $businesses = $query->get();
            
            return response()->json([
                'success' => true,
                'data' => $businesses
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get providers: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to get providers',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Send job to selected providers
     *
     * @param Request $request
     * @param string $jobUuid
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendToProviders(Request $request, $jobUuid)
    {
        try {
            $request->validate([
                'provider_uuids' => 'required|array|min:1',
                'provider_uuids.*' => 'required|exists:businesses,business_uuid'
            ]);

            $job = JobBooking::where('job_uuid', $jobUuid)->firstOrFail();
            $businesses = Business::whereIn('business_uuid', $request->provider_uuids)->get();

            foreach ($businesses as $business) {
                if (empty($business->email)) {
                    continue;
                }
                \Mail::to($business->email)
                    ->send(new \App\Mail\NewJobRequest($job, $business));
            }

            return response()->json([
                'success' => true,
                'message' => 'Project has been sent to ' . $businesses->count() . ' providers.'
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'JOB_NOT_FOUND',
                    'message' => 'Job not found'
                ]
            ], 404);
        } catch (\Exception $e) {
            Log::error('Failed to send to providers: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to send project to providers',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    public function getMyJobBookings(Request $request)
    {
        $user = auth('api')->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'UNAUTHORIZED',
                    'message' => 'Unauthorized',
                ]
            ], 401);
        }
        $perPage = $request->input('per_page', 15);
        $jobs = $this->repository->getAll(['user_id' => $user->id], $perPage);
        return response()->json([
            'success' => true,
            'data' => JobBookingResource::collection($jobs),
            'pagination' => [
                'current_page' => $jobs->currentPage(),
                'per_page' => $jobs->perPage(),
                'total' => $jobs->total(),
                'last_page' => $jobs->lastPage()
            ]
        ]);
    }



    /**
     * Accept a bid for the specified job booking.
     *
     * @param  \App\Http\Requests\API\AcceptBidRequest  $request
     * @param  \App\Models\JobBooking  $jobBooking
     * @return \Illuminate\Http\JsonResponse
     */
    public function acceptBid(AcceptBidRequest $request, JobBooking $jobBooking)
    {
        try {
            // Check authorization
            $this->authorize('acceptBid', $jobBooking);

            $bidId = $request->input('bid_id');
            $notes = $request->input('notes');

            // Verify the bid belongs to this job booking
            $bid = $jobBooking->bids()->find($bidId);
            if (!$bid) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'BID_NOT_FOUND',
                        'message' => 'Bid not found for this job booking',
                    ]
                ], 404);
            }

            // Check if bid can be accepted
            if (!$bid->canAccept()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CANNOT_ACCEPT_BID',
                        'message' => 'This bid cannot be accepted',
                    ]
                ], 422);
            }

            // Accept the bid using the model method
            $success = $jobBooking->acceptBid($bidId, $notes);

            if ($success) {
                // Reload the job with relationships
                $jobBooking->load(['bids', 'acceptedBid', 'assignedJob']);

                return response()->json([
                    'success' => true,
                    'data' => new JobBookingResource($jobBooking),
                    'message' => 'Bid accepted successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'ACCEPT_BID_FAILED',
                        'message' => 'Failed to accept bid',
                    ]
                ], 422);
            }
        } catch (\Exception $e) {
            Log::error('Failed to accept bid: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to accept bid',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }
}